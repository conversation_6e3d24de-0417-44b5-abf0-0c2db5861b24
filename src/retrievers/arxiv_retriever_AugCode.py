"""
arXiv论文检索器
使用arXiv API获取论文数据
"""

import arxiv
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..models.paper_AugCode import Paper
from .base_retriever_AugCode import BaseRetriever


class ArxivRetriever(BaseRetriever):
    """arXiv论文检索器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化arXiv检索器
        
        Args:
            config: arXiv平台配置
        """
        super().__init__(config)
        self.base_url = config.get('base_url', 'http://export.arxiv.org/api/query')
        self.categories = config.get('categories', ['cs.LG', 'cs.AI'])
        self.query_templates = config.get('query_templates', {})
        
        # 创建arXiv客户端
        self.client = arxiv.Client(
            page_size=100,
            delay_seconds=1,
            num_retries=3
        )
        
        self.logger = logging.getLogger(__name__)
    
    def fetch_papers(self, start_date: datetime, end_date: datetime, 
                    max_results: int = 100) -> List[Paper]:
        """获取指定日期范围内的论文
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        all_papers = []
        
        # 使用多种查询策略
        queries = self._build_queries(start_date, end_date)
        
        # 并发执行查询
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_query = {
                executor.submit(self._execute_query, query, max_results): query
                for query in queries
            }
            
            for future in as_completed(future_to_query):
                query = future_to_query[future]
                try:
                    papers = future.result()
                    all_papers.extend(papers)
                    self.logger.info(f"查询 '{query[:50]}...' 获取到 {len(papers)} 篇论文")
                except Exception as e:
                    self.logger.error(f"查询 '{query[:50]}...' 失败: {e}")
        
        # 去重
        unique_papers = self._deduplicate_papers(all_papers)
        self.logger.info(f"去重后剩余 {len(unique_papers)} 篇论文")
        
        return unique_papers
    
    def _build_queries(self, start_date: datetime, end_date: datetime) -> List[str]:
        """构建查询字符串列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            查询字符串列表
        """
        queries = []
        
        # 格式化日期
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
        
        # 1. 基于分类的查询
        for category in self.categories:
            query = f'cat:{category} AND submittedDate:[{start_str}* TO {end_str}*]'
            queries.append(query)
        
        # 2. 基于查询模板的查询
        for template_name, template_query in self.query_templates.items():
            # 添加日期限制
            query = f'({template_query}) AND submittedDate:[{start_str}* TO {end_str}*]'
            queries.append(query)
        
        # 3. 通用蛋白质/抗体查询
        protein_query = f'(protein OR antibody) AND (design OR prediction OR structure OR function) AND submittedDate:[{start_str}* TO {end_str}*]'
        queries.append(protein_query)
        
        return queries
    
    def _execute_query(self, query: str, max_results: int) -> List[Paper]:
        """执行单个查询
        
        Args:
            query: 查询字符串
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        papers = []
        
        try:
            search = arxiv.Search(
                query=query,
                max_results=max_results,
                sort_by=arxiv.SortCriterion.SubmittedDate,
                sort_order=arxiv.SortOrder.Descending
            )
            
            for result in self.client.results(search):
                try:
                    paper = self._parse_arxiv_result(result)
                    if paper:
                        papers.append(paper)
                except Exception as e:
                    self.logger.error(f"解析论文 {result.entry_id} 失败: {e}")
                    continue
                
                # 添加延迟避免请求过快
                time.sleep(0.1)
            
        except Exception as e:
            self.logger.error(f"执行查询失败: {e}")
        
        return papers
    
    def _parse_arxiv_result(self, result: arxiv.Result) -> Optional[Paper]:
        """解析arXiv查询结果
        
        Args:
            result: arXiv查询结果
            
        Returns:
            Paper对象或None
        """
        try:
            # 提取基本信息
            paper_id = result.get_short_id()
            title = result.title.strip()
            authors = [author.name for author in result.authors]
            abstract = result.summary.replace('\n', ' ').strip()
            categories = result.categories
            published_date = result.published
            url = result.entry_id
            pdf_url = result.pdf_url
            comments = result.comment
            
            # 创建Paper对象
            paper = Paper(
                id=paper_id,
                title=title,
                authors=authors,
                abstract=abstract,
                categories=categories,
                published_date=published_date,
                platform='arxiv',
                url=url,
                pdf_url=pdf_url,
                comments=comments
            )
            
            return paper
            
        except Exception as e:
            self.logger.error(f"解析arXiv结果失败: {e}")
            return None
    
    def _deduplicate_papers(self, papers: List[Paper]) -> List[Paper]:
        """去重论文列表
        
        Args:
            papers: 原始论文列表
            
        Returns:
            去重后的论文列表
        """
        seen_hashes = set()
        seen_ids = set()
        unique_papers = []
        
        for paper in papers:
            # 基于ID去重
            if paper.id in seen_ids:
                continue
            
            # 基于内容哈希去重
            if paper.hash in seen_hashes:
                continue
            
            seen_ids.add(paper.id)
            seen_hashes.add(paper.hash)
            unique_papers.append(paper)
        
        return unique_papers
    
    def get_paper_by_id(self, paper_id: str) -> Optional[Paper]:
        """根据ID获取单篇论文
        
        Args:
            paper_id: 论文ID
            
        Returns:
            Paper对象或None
        """
        try:
            search = arxiv.Search(id_list=[paper_id])
            result = next(self.client.results(search))
            return self._parse_arxiv_result(result)
        except Exception as e:
            self.logger.error(f"获取论文 {paper_id} 失败: {e}")
            return None
    
    def search_by_keywords(self, keywords: List[str], max_results: int = 50) -> List[Paper]:
        """根据关键词搜索论文
        
        Args:
            keywords: 关键词列表
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        # 构建关键词查询
        keyword_query = ' OR '.join([f'"{keyword}"' for keyword in keywords])
        query = f'({keyword_query})'
        
        return self._execute_query(query, max_results)
    
    def get_recent_papers(self, days: int = 1, max_results: int = 100) -> List[Paper]:
        """获取最近几天的论文
        
        Args:
            days: 天数
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        return self.fetch_papers(start_date, end_date, max_results)
    
    def get_papers_by_category(self, category: str, days: int = 1, 
                              max_results: int = 100) -> List[Paper]:
        """获取指定分类的论文
        
        Args:
            category: arXiv分类
            days: 天数
            max_results: 最大结果数
            
        Returns:
            论文列表
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
        
        query = f'cat:{category} AND submittedDate:[{start_str}* TO {end_str}*]'
        
        return self._execute_query(query, max_results)
    
    def validate_connection(self) -> bool:
        """验证与arXiv的连接
        
        Returns:
            连接是否正常
        """
        try:
            # 尝试获取一篇论文
            search = arxiv.Search(
                query='cat:cs.LG',
                max_results=1
            )
            
            result = next(self.client.results(search))
            return result is not None
            
        except Exception as e:
            self.logger.error(f"arXiv连接验证失败: {e}")
            return False
