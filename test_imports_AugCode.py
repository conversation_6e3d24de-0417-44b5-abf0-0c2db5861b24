#!/usr/bin/env python3
"""
测试所有模块导入
验证系统模块是否能正确导入
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块导入"""
    print("🧪 测试模块导入...")
    print("=" * 50)
    
    tests = [
        # 配置管理
        ("配置管理器", "src.config.config_manager_AugCode", "ConfigManager"),
        
        # 数据模型
        ("论文数据模型", "src.models.paper_AugCode", "Paper"),
        
        # 检索器
        ("基础检索器", "src.retrievers.base_retriever_AugCode", "BaseRetriever"),
        ("arXiv检索器", "src.retrievers.arxiv_retriever_AugCode", "ArxivRetriever"),
        ("bioRxiv检索器", "src.retrievers.biorxiv_retriever_AugCode", "BioRxivRetriever"),
        
        # 筛选器
        ("基础筛选器", "src.filters.base_filter_AugCode", "BaseFilter"),
        ("关键词筛选器", "src.filters.keyword_filter_AugCode", "KeywordFilter"),
        ("分类筛选器", "src.filters.category_filter_AugCode", "CategoryFilter"),
        ("语义筛选器", "src.filters.semantic_filter_AugCode", "SemanticFilter"),
        ("去重筛选器", "src.filters.deduplication_filter_AugCode", "DeduplicationFilter"),
        ("筛选器管道", "src.filters.filter_pipeline_AugCode", "FilterPipeline"),
        
        # 存储
        ("数据库管理器", "src.storage.database_AugCode", "DatabaseManager"),
        
        # 输出格式化
        ("基础格式化器", "src.outputs.base_formatter_AugCode", "BaseFormatter"),
        ("JSON格式化器", "src.outputs.json_formatter_AugCode", "JSONFormatter"),
        ("Markdown格式化器", "src.outputs.markdown_formatter_AugCode", "MarkdownFormatter"),
        ("HTML格式化器", "src.outputs.html_formatter_AugCode", "HTMLFormatter"),
        ("格式化器工厂", "src.outputs.formatter_factory_AugCode", "FormatterFactory"),
        
        # 工具
        ("日志工具", "src.utils.logger_AugCode", "setup_logger"),
        
        # 调度器
        ("本地调度器", "src.schedulers.local_scheduler_AugCode", "LocalScheduler"),
    ]
    
    passed = 0
    failed = 0
    
    for name, module_path, class_name in tests:
        try:
            module = __import__(module_path, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"✅ {name}: {module_path}.{class_name}")
            passed += 1
        except Exception as e:
            print(f"❌ {name}: {module_path}.{class_name} - {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有模块导入成功！")
        return True
    else:
        print("❌ 部分模块导入失败，请检查代码。")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    print("=" * 50)
    
    try:
        # 测试配置管理器
        from src.config.config_manager_AugCode import ConfigManager
        
        # 检查配置文件是否存在
        config_file = project_root / "config" / "config_AugCode.yaml"
        if not config_file.exists():
            print("❌ 配置文件不存在，请先运行: python run_monitor_AugCode.py --setup")
            return False
        
        config_manager = ConfigManager(str(config_file))
        print("✅ 配置管理器初始化成功")
        
        # 测试论文数据模型
        from src.models.paper_AugCode import Paper
        from datetime import datetime
        
        test_paper = Paper(
            id="test_001",
            title="Test Paper",
            authors=["Test Author"],
            abstract="This is a test abstract.",
            categories=["cs.LG"],
            published_date=datetime.now(),
            platform="test",
            url="https://example.com"
        )
        print("✅ 论文数据模型创建成功")
        
        # 测试数据库管理器
        from src.storage.database_AugCode import DatabaseManager
        
        # 使用临时数据库
        temp_db = project_root / "test_temp.db"
        db_manager = DatabaseManager(str(temp_db))
        print("✅ 数据库管理器初始化成功")
        
        # 清理临时文件
        if temp_db.exists():
            temp_db.unlink()
        
        print("🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧬 蛋白质/抗体AI研究论文监控系统 - 模块导入测试")
    print("=" * 60)
    
    # 测试模块导入
    import_success = test_imports()
    
    if import_success:
        # 测试基本功能
        func_success = test_basic_functionality()
        
        if func_success:
            print("\n🎉 所有测试通过！系统准备就绪。")
            print("\n下一步:")
            print("1. 初始化系统: python run_monitor_AugCode.py --setup")
            print("2. 运行测试: python run_monitor_AugCode.py --test")
            print("3. 试运行: python main_AugCode.py --dry-run")
        else:
            print("\n❌ 功能测试失败。")
            sys.exit(1)
    else:
        print("\n❌ 模块导入测试失败。")
        sys.exit(1)

if __name__ == "__main__":
    main()
