#!/usr/bin/env python3
"""
简单的导入测试脚本
验证核心模块是否能正确导入
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """测试核心模块导入"""
    print("🧪 测试核心模块导入...")
    
    try:
        # 测试配置管理器
        print("1. 测试配置管理器...")
        from src.config.config_manager_AugCode import ConfigManager
        print("   ✅ ConfigManager 导入成功")
        
        # 测试数据模型
        print("2. 测试数据模型...")
        from src.models.paper_AugCode import Paper
        print("   ✅ Paper 导入成功")
        
        # 测试检索器
        print("3. 测试检索器...")
        from src.retrievers.base_retriever_AugCode import BaseRetriever
        from src.retrievers.arxiv_retriever_AugCode import ArxivRetriever
        print("   ✅ 检索器导入成功")
        
        # 测试筛选器
        print("4. 测试筛选器...")
        from src.filters.base_filter_AugCode import BaseFilter
        from src.filters.keyword_filter_AugCode import KeywordFilter
        print("   ✅ 筛选器导入成功")
        
        # 测试存储
        print("5. 测试存储...")
        from src.storage.database_AugCode import DatabaseManager
        print("   ✅ 数据库管理器导入成功")
        
        # 测试输出格式化
        print("6. 测试输出格式化...")
        from src.outputs.formatter_factory_AugCode import FormatterFactory
        print("   ✅ 格式化器导入成功")
        
        print("\n🎉 所有核心模块导入成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_creation():
    """测试基本对象创建"""
    print("\n🔧 测试基本对象创建...")
    
    try:
        # 创建配置管理器（使用示例配置）
        config_file = project_root / "config" / "config_AugCode.yaml"
        if config_file.exists():
            from src.config.config_manager_AugCode import ConfigManager
            config_manager = ConfigManager(str(config_file))
            print("   ✅ 配置管理器创建成功")
        else:
            print("   ⚠️  配置文件不存在，跳过配置管理器测试")
        
        # 创建论文对象
        from src.models.paper_AugCode import Paper
        from datetime import datetime
        
        paper = Paper(
            id="test_001",
            title="Test Paper Title",
            authors=["Test Author"],
            abstract="This is a test abstract for the paper.",
            categories=["cs.LG"],
            published_date=datetime.now(),
            platform="test",
            url="https://example.com/test"
        )
        print("   ✅ 论文对象创建成功")
        
        # 创建数据库管理器
        from src.storage.database_AugCode import DatabaseManager
        temp_db_path = project_root / "temp_test.db"
        db_manager = DatabaseManager(str(temp_db_path))
        print("   ✅ 数据库管理器创建成功")
        
        # 清理临时文件
        if temp_db_path.exists():
            temp_db_path.unlink()
        
        print("\n🎉 基本对象创建测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 对象创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧬 蛋白质/抗体AI研究论文监控系统 - 简单测试")
    print("=" * 60)
    
    # 测试核心模块导入
    import_success = test_core_imports()
    
    if import_success:
        # 测试基本对象创建
        creation_success = test_basic_creation()
        
        if creation_success:
            print("\n🎉 所有测试通过！系统模块正常工作。")
            print("\n下一步建议:")
            print("1. 运行完整测试: python test_imports_AugCode.py")
            print("2. 初始化系统: python run_monitor_AugCode.py --setup")
            print("3. 运行系统测试: python run_monitor_AugCode.py --test")
            print("4. 试运行主程序: python main_AugCode.py --dry-run")
        else:
            print("\n❌ 对象创建测试失败。")
            return False
    else:
        print("\n❌ 模块导入测试失败。")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
