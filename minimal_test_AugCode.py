#!/usr/bin/env python3
"""
最小化测试脚本 - 验证导入问题是否解决
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("Testing imports...")
    
    try:
        # 测试配置管理器
        from src.config.config_manager_AugCode import ConfigManager
        print("✅ ConfigManager imported successfully")
        
        # 测试数据模型
        from src.models.paper_AugCode import Paper
        print("✅ Paper model imported successfully")
        
        # 测试检索器
        from src.retrievers.arxiv_retriever_AugCode import ArxivRetriever
        print("✅ ArxivRetriever imported successfully")
        
        # 测试筛选器
        from src.filters.keyword_filter_AugCode import KeywordFilter
        print("✅ KeywordFilter imported successfully")
        
        # 测试数据库
        from src.storage.database_AugCode import DatabaseManager
        print("✅ DatabaseManager imported successfully")
        
        print("\n🎉 All core modules imported successfully!")
        print("The import issues have been resolved.")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\nNext steps:")
        print("1. Run: python run_monitor_AugCode.py --setup")
        print("2. Run: python run_monitor_AugCode.py --test")
        print("3. Run: python main_AugCode.py --dry-run")
    sys.exit(0 if success else 1)
